<template>
    <div class="equipmentAlarmAssembly">
        <div class="alarmSevenDays">
            <div class="alarmSevenDays_title">
                近七天报警趋势
            </div>
            <div class="alarmSevenDays_chart">
                <znyEchart :chartData="alarmSevenDaysOption" :chartType="'line'"></znyEchart>
            </div>
        </div>
        <div class="sensorAlarmNum">
            <div class="sensorAlarmNum_title" >
                报警记录
            </div>
            <div class="sensorAlarmNum_line"></div>
            <div class="sensorAlarmNum_chart" @click="AlarmRecordDialogOpen">
                <div class="sensorAlarmNum_list" id="sensorAlarmNum_scroll_box">
                    <vue-seamless-scroll :data="realPriceList" :class-option="classOption" class="seamless-warp" style="width: 100%">
                        <div id="sensorAlarmNum_scroll1">
                            <div class="list_item" v-for="(item, index) in realPriceList" :key="index">
                                <div class="item" v-if="index < 9">0{{ index + 1 }}</div>
                                <div class="item" v-else>{{ index + 1 }}</div>
                                <div class="item">{{ item.areaName }}</div>
                                <div class="item">{{ item.alarmType | alarmTypeFormat }}</div>
                                <div class="item">
                                    <div>起</div>
                                    <div>止</div>
                                </div>
                                <div class="item">
                                    <div>{{ item.startTime }}</div>
                                    <div>{{ item.endTime | endTimeFormat }}</div>
                                </div>
                            </div>
                        </div>
                    </vue-seamless-scroll>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { chart } from '../js/chart.js'
export default {
    props: {
        type: {
            type: Number,
            default: 0,
        },
        alarmCollect:{
            type:Object,
            default:new Object
        },
        areaId: {
            type: [String, Number],
            default: null,
        },
    },
    data() {
        return {
            alarmSevenDaysOption: {
                //近七天报警数折线图数据
                show: true,
                option: {},
            },
            realPriceList: [],//作物实时价格列表数据
            classOption: {
                step: 0.5
            },
        }
    },
    filters:{
        endTimeFormat(msg) {
            if(msg==null){
                return "正在报警"
            }else{
                return msg
            }
        },
    },
    watch:{
        alarmCollect(){
            chart.alarmSevenDaysLine(this.alarmCollect.recentAlarmList).then(data => {
                this.alarmSevenDaysOption = data
            })
            this.realPriceList=this.alarmCollect.alarmDataList
        },
    },
    mounted() {
        
    },
    methods: {
        // 报警记录弹窗打开
        AlarmRecordDialogOpen() {
            this.zEmit('AlarmDialogOpen', {
                type: this.type,
                areaId: this.areaId
            })
        },
    },
    beforeDestroy() {
        const that = this
        Object.assign(that.$data, that.$options.data())
    },
}
</script>
<style lang="less">
@import '../assets/css/equipmentAlarm.less';
</style>
