.batchInforManagement{
  width: 100%;
  height: 97%;
  &_tabList{
    margin: 22px 32px;
    display: flex;
    align-items: center;
    .tabList_item{
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      margin-right: 40px;
      cursor: pointer;
    }
    .active{
      font-size: 22px;
      font-weight: bold;
      color: #0093BC;
    }
  }
  &_con{
    width: 96%;
    height: 91%;
    background: #FFFFFF;
    border-radius: 8px;
    margin: 20px 0 0 32px;
    border: 1px solid transparent;
    .handleBox,.handleBox1{
      display: flex;
      align-items: center;
      &_item{
        height: 40px;
        margin-top: 24px;
        margin-right: 16px;
      }
      &_item:nth-child(1){
        width: 200px;
        margin-left: 20px;
      }
      &_item:nth-child(2){
        width: 200px;
        margin-left: 6px;
      }
      &_item:nth-child(3),
      &_item:nth-child(4){
        width: 80px;
      }
      &_item:nth-child(5),
      &_item:nth-child(6), &_item:nth-child(8){
        width: 132px;
      }
      &_item:nth-child(7){
        width: 100px;
      }
      &_item1{
        width: 100px !important;
      }
      &_text{
        margin: 18px 20px 0 23px;
      }
      &_btn{
        width: 100px;
        height: 40px;
      }
    }
    .handleBox1{
      .handleBox_btn{
        margin-top: 24px;
      }
    }
    .tableBox{
      width: 97%;
      height: 82%;
      margin: auto;
      margin-top: 24px;
      .cell{
        .viewData{
          font-size: 15px;
          font-weight: 400;
          color: #007EFF;
          cursor: pointer;
          margin: 0 8px;
        }
      }
    }
    .pageBox{
      width: 97%;
      height: 5%;
      margin-top: 10px;
      text-align: center;
    }
  }
  .el-dialog{
    .btnBox{
      margin-top: 15px;
      display: flex;
      justify-content: center;
      .btnItem{
        width: 160px;
        height: 40px;
        margin: 0 15px;
      }
    }
  }
  .detailsDialog{
    .el-dialog{
      height: 600px;
      .el-dialog__body{
        height: 505px;
        .handleBox{
          display: flex;
          &_item{
            display: flex;
            height: 48px;
            line-height: 48px;
            margin-right: 40px;
            .input_item{
              margin-left: 16px;
            }
          }
          &_item:nth-child(1){
            .input_item{
              width: 140px;
            }
          }
          &_item:nth-child(2),
          &_item:nth-child(3){
            .input_item{
              width: 260px;
            }
          }
        }
        .tableBox{
          margin-top: 20px;
          height: 440px;
        }
      }
    }
  }
  .harvestBatchDialog{
    .el-dialog{
      height: 460px;
      margin-top: 25vh !important;
      .el-dialog__body{
        height: 365px;
        .tipsText{
          margin: 0 0 24px 0;
          span{
            color: #007EFF;
          }
        }
        .formBox{
          height: 270px;
          overflow: scroll;
          .systemFormStyle{
            width: 360px;
            height: 48px;
          }
          .el-form-item:nth-child(1){
            .el-form-item__content{
              position: relative;
              .tips{
                width: 18px;
                height: 18px;
                position: absolute;
                top: -32px;
                left: 72px;
                // height: 12px;
                font-size: 12px;
                font-weight: 400;
                img{
                  width: 18px;
                  height: 18px;
                }
              }
              .systemFormStyle:not(:first-child){
                margin-top: 12px;
              }
              .systemFormStyle{
                position: relative;
                .reduce-icon{
                  position: absolute;
                  top: 15px;
                  right: -30px;
                }
              }
            }
          }
        }
      }
    }
  }
  .tipsDialog{
    .el-dialog{
      margin-top: 35vh !important;
      .el-dialog__body{
        .tipsText{
          margin: 0 30px 40px 30px;
          span{
            color: #007EFF;
          }
        }
      }
    }
  }
  .tipsDialog3{
    .el-dialog{
      .el-dialog__body{
        .tipsText{
          margin: 0 0 40px 0;
        }
      }
    }
  }
  .tipsDialog4{
    .el-dialog{
      .el-dialog__body{
        .tipsText{
          margin: 0 0 40px 0;
        }
      }
    }
  }
  .plantBatchDialog{
    .el-dialog{
      height: 460px;
      margin-top: 25vh !important;
      .el-dialog__body{
        height: 365px;
        .formBox{
          height: 305px;
          overflow: scroll;
          .systemFormStyle{
            width: 360px;
            height: 48px;
          }
          .el-form-item:nth-child(2){
            .el-form-item__content{
              position: relative;
              .tips{
                width: 18px;
                height: 18px;
                position: absolute;
                top: -35px;
                left: 75px;
                // height: 12px;
                font-size: 12px;
                font-weight: 400;
                img{
                  width: 18px;
                  height: 18px;
                }
              }
              .systemFormStyle:not(:first-child){
                margin-top: 12px;
              }
              .systemFormStyle{
                position: relative;
                .reduce-icon{
                  position: absolute;
                  top: 15px;
                  right: -30px;
                }
              }
            }
          }
        }
      }
    }
  }
  .plantBatchEndDialog{
    .el-dialog{
      height: 460px;
      margin-top: 25vh !important;
      .el-dialog__body{
        height: 365px;
        .formBox{
          height: 290px;
          .systemFormStyle{
            width: 395px;
            height: 48px;
          }
        }
      }
    }
    
  }
  .batchHistoryDialog{
    .el-dialog{
      height: 690px;
      margin-top: 9vh !important;
      .el-dialog__body{
        height: 427px;
        .handleBox{
          display: flex;
          align-items: center;
          &_item{
            height: 40px;
            margin-bottom: 24px;
            margin-right: 24px;
          }
          &_item:nth-child(1),
          &_item:nth-child(2){
            width: 270px;
          }
          &_item:nth-child(3){
            width: 80px;
          }
        }
        .tableBox{
          height: 527px;
        }
      }
    }
  }
  .outputValueDialog,.yieldDialog{
    .el-dialog{
      height: 340px;
      margin-top: 25vh !important;
      .el-dialog__body{
        .el-form-item__label{
          line-height: 40px !important;
        }
        .systemFormStyle{
          width: 220px;
          height: 40px;
        }
      }
    }
  }
  .outputReportDialog{
    .el-dialog{
      margin-top: 30vh !important;
      .el-dialog__body{
        .el-form-item__label{
          line-height: 40px !important;
        }
        .el-form-item{
          .systemFormStyle{
            width: 220px;
            height: 40px;
          }
        }
      }
    }
  }
  .updateGrowStageDialog{
    .el-dialog{
      height: 600px;
      margin-top: 15vh !important;
      .el-dialog__body{
        height: 505px;
        .formBox{
          height: 440px;
          overflow-y: auto;
          .systemFormStyle{
            width: 100%;
            height: 48px;
          }
          // 简单提示样式
          .simple-tip {
            margin-top: 8px;
            font-size: 12px;
            display: flex;
            align-items: center;
          }
          // 加载状态样式
          .loading-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #666;
          }
          // 无数据提示样式
          .no-data-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #999;
            font-size: 14px;
            i {
              margin-right: 8px;
              font-size: 16px;
            }
          }
          // 批次卡片容器
          .batch-cards {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 12px;
          }
          // 批次卡片样式
          .batch-card {
            border: 2px solid #e6e6e6;
            border-radius: 8px;
            padding: 16px;
            background-color: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              border-color: #409EFF;
              background-color: #f0f8ff;
            }
            &.selected {
              border-color: #409EFF;
              background-color: #e6f4ff;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            }
          }
          // 批次卡片头部
          .batch-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            .batch-title {
              font-weight: bold;
              font-size: 16px;
              color: #333;
            }
            .current-stage {
              font-size: 14px;
              color: #666;
              background-color: #f0f0f0;
              padding: 4px 8px;
              border-radius: 4px;
            }
          }
          // 批次卡片内容
          .batch-content {
            .info-row {
              display: flex;
              margin-bottom: 8px;
              font-size: 14px;
              .label {
                color: #666;
                min-width: 80px;
                margin-right: 8px;
              }
              .value {
                color: #333;
                flex: 1;
              }
            }
            .info-row:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}