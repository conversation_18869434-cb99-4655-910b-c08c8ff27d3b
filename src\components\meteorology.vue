<template>
    <div class="meteorology">
        <div class="meteorology_left">
            <!-- <floatingWindow v-if="floatingWindowboole" /> -->
            <!-- 智慧物联 -->
            <div class="meteorology_left_smartIOTData">
                <div class="title meteorology_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智慧物联数据</span>
                </div>
                <div class="meteorology_left_smartIOTData_con">
                    <div class="meteorology_left_smartIOTData_con_topList">
                        <div class="topList1">
                            <div class="topList_item">
                                <div class="item_img">
                                    <img src="../assets/image/monitoringCenter/icon1.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="basicData.temperature==null">--</div>
                                    <div v-else>{{ basicData.temperature }}℃</div>
                                    <div>温度</div>
                                </div>
                            </div>
                            <div class="topList_item">
                                <div class="item_img">
                                    <img src="../assets/image/monitoringCenter/icon2.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="basicData.humidity==null">--</div>
                                    <div v-else>{{ basicData.humidity }}%RH</div>
                                    <div>湿度</div>
                                </div>
                            </div>
                        </div>
                        <div class="topList2">
                            <div class="topList_item">
                                <div class="item_img">
                                    <img src="../assets/image/centralControlPlatform/icon4.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="basicData.precipitation==null">--</div>
                                    <div v-else>{{ basicData.precipitation }}mm</div>
                                    <div>雨量</div>
                                </div>
                            </div>
                            <div class="topList_item">
                                <div class="item_img">
                                    <img src="../assets/image/centralControlPlatform/icon5.png" alt="" />
                                </div>
                                <div class="item_text">
                                    <div v-if="basicData.windLevel==null">--</div>
                                    <div v-else>
                                        {{ basicData.windLevel }}级{{ basicData.windDirection | windDirectionFormat }}
                                    </div>
                                    <div>风况</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="meteorology_left_smartIOTData_con_bottomList">
                        <SmartIOTDataSwiper :basicData="basicData" />
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 设备报警 -->
            <div class="meteorology_left_equipmentAlarm">
                <div class="title meteorology_left_equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>气象报警</span>
                </div>
                <div class="meteorology_left_equipmentAlarm_con">
                    <EquipmentAlarm :type="1" :alarmCollect="alarmCollect" />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="meteorology_right">
            <div class="title meteorology_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="meteorology_right_btn" @click="floatingWindow">
                <EquipmentButton />
            </div>
            <!-- <div class="meteorology_right_con" @click="meteorologyEMDialogOpen"> -->
            <div class="meteorology_right_con">
                <znyAMap :type="1"></znyAMap>
                <!-- <div class="twoDimensionalMap" @click="meteorologyEMDialogOpen"></div> -->
            </div>
        </div>

        <!-- 报警记录弹窗 -->
        <AlarmDialog />
    </div>
</template>
<script>
import znyAMap from '../components/znyAMap.vue'
import EquipmentAlarm from '../components/equipmentAlarm.vue'
import SmartIOTDataSwiper from '../components/smartIOTDataSwiper.vue'
import EquipmentButton from '../components/equipmentButton.vue'
import AlarmDialog from '../components/Dialog/AlarmDialog.vue'

import WeatherService from '../jaxrs/concrete/com.zny.ia.api.WeatherService.js'
export default {
    components: {
        EquipmentAlarm,
        SmartIOTDataSwiper,
        EquipmentButton,
        znyAMap,
        AlarmDialog,
    },
    data() {
        return {
            basicData: {}, //基础数据
            alarmCollect: {}, //报警信息
            floatingWindowboole: false,
        }
    },
    mounted() {
        this.getWeatherBasicData()
        this.getWeatherAlarmCollect()
    },
    methods: {
        // 获取气象基础数据
        getWeatherBasicData() {
            WeatherService.weatherBasicData().then(res => {
                this.basicData = res
            })
        },
        // 获取气象报警数据
        getWeatherAlarmCollect() {
            WeatherService.weatherAlarmCollect().then(res => {
                this.alarmCollect = res
            })
        },
        floatingWindow() {
            this.$router.push('/floatingWindow')
            
        },
        // // 气象环境检测系统弹窗
        // meteorologyEMDialogOpen(){
        //   this.zEmit('meteorologyEMDialogOpen');
        // },
    },
}
</script>
<style lang="less">
@import '../assets/css/meteorology.less';
</style>
